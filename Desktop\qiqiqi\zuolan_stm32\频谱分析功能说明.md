# 频谱分析功能说明

## 功能概述

本功能通过串口屏按键5触发，实现对未知滤波器的频率响应特性分析。系统使用DDS产生扫频信号，经过待测滤波器后由AD采集，然后通过FFT分析得到滤波器的频率响应曲线。

## 工作原理

### 信号链路
```
DDS扫频信号 → 未知滤波器 → AD采集 → FFT分析 → 频率响应特性
```

### 测试流程
1. **扫频信号生成**: DDS产生100Hz到100kHz的扫频信号
2. **信号调理**: 扫频信号经过待测的未知滤波器
3. **数据采集**: AD以1MHz采样率采集滤波器输出信号
4. **频谱分析**: 对每个频率点的采集数据进行FFT分析
5. **特性提取**: 计算滤波器的幅度响应和关键参数
6. **结果输出**: 通过串口输出详细的分析结果

## 技术参数

### 扫频参数
- **频率范围**: 100Hz - 100kHz
- **频率步数**: 51个频率点
- **步进大小**: 约2kHz
- **稳定时间**: 每个频率点50ms

### 采集参数
- **采样频率**: 1MHz
- **采样点数**: 1024点（AD_FIFO_SIZE）
- **FFT长度**: 1024点
- **窗函数**: Hanning窗

### 分析参数
- **频率分辨率**: 约977Hz (1MHz/1024)
- **幅度精度**: 浮点数精度
- **相对dB计算**: 20*log10(幅度/最大幅度)

## 使用方法

### 1. 硬件连接
```
DDS输出 → 待测滤波器输入
待测滤波器输出 → AD输入通道1
```

### 2. 操作步骤
1. 确保硬件连接正确
2. 在串口屏上按下按键5
3. 系统自动开始扫频测试
4. 通过串口1查看实时测试进度和结果

### 3. 结果解读
系统会输出以下信息：
- 实时扫频进度
- 每个频率点的幅度响应
- 滤波器特性分析
- 详细的频率响应数据表

## 输出结果

### 实时进度信息
```
=== 开始频谱分析 ===
扫频范围: 100Hz - 100kHz
目标: 分析未知滤波器的频率响应

开始扫频测试...
测试频率: 100 Hz (1/51)
  -> 幅度响应: 0.123456 V (FFT bin 0)
测试频率: 2100 Hz (2/51)
  -> 幅度响应: 0.234567 V (FFT bin 2)
...
```

### 特性分析结果
```
=== 扫频完成，分析结果 ===
滤波器特性分析:
  最大响应: 0.987654 V @ 5000 Hz
  -3dB带宽: 3000 Hz (3500 Hz - 6500 Hz)

滤波器类型分析:
  可能是带通滤波器 (峰值在中频段)
  中心频率: 5000 Hz
  品质因数Q ≈ 1.67
```

### 详细数据表
```
详细频率响应数据:
频率(Hz)    幅度(V)        相对dB
100         0.123456      -18.12
2100        0.234567      -12.45
4100        0.567890      -4.78
6100        0.987654       0.00
8100        0.456789      -6.89
...
```

## 滤波器类型识别

### 低通滤波器
- **特征**: 峰值在低频段（前1/3频率范围）
- **输出**: "可能是低通滤波器 (峰值在低频段)"

### 高通滤波器
- **特征**: 峰值在高频段（后1/3频率范围）
- **输出**: "可能是高通滤波器 (峰值在高频段)"

### 带通滤波器
- **特征**: 峰值在中频段
- **输出**: 中心频率和品质因数Q值

## 关键参数说明

### -3dB带宽
- **定义**: 幅度下降到最大值的70.7%（-3dB）时的频率范围
- **计算**: 上限频率 - 下限频率
- **意义**: 表示滤波器的有效通带宽度

### 品质因数Q
- **定义**: Q = 中心频率 / -3dB带宽
- **意义**: 表示滤波器的选择性，Q值越高选择性越好

### 相对dB
- **定义**: 20*log10(当前幅度/最大幅度)
- **意义**: 以最大响应为0dB基准的相对衰减

## 注意事项

### 1. 硬件要求
- DDS输出幅度应适合滤波器输入范围
- 滤波器输出应在AD输入范围内
- 确保信号链路阻抗匹配

### 2. 测试环境
- 避免外部干扰信号
- 确保电源稳定
- 保持温度稳定

### 3. 结果解释
- 结果仅反映当前测试条件下的特性
- 滤波器类型判断为自动识别，需结合实际情况
- 相位响应信息当前版本未实现

## 扩展功能

### 可扩展的功能
1. **相位响应分析**: 增加相位测量功能
2. **群延迟计算**: 计算滤波器的群延迟特性
3. **自动增益控制**: 根据信号幅度自动调整DDS输出
4. **数据导出**: 将测试结果保存到文件
5. **图形显示**: 在串口屏上显示频率响应曲线

### 参数可调
- 扫频范围可通过修改代码调整
- 频率步数可根据精度要求调整
- 稳定时间可根据滤波器特性调整

## 故障排除

### 1. 无响应或响应很小
- 检查DDS输出是否正常
- 检查滤波器连接
- 检查AD输入连接
- 调整DDS输出幅度

### 2. 结果异常
- 检查采样频率设置
- 检查FFT初始化
- 验证频率计算公式
- 检查数据缓冲区

### 3. 类型识别错误
- 手动分析频率响应数据
- 调整类型判断阈值
- 结合实际滤波器规格验证

## 开发者信息

- **模块**: hmi_key_handler.c/h
- **主要函数**: `Start_Spectrum_Analysis()`, `Analyze_Filter_Response()`
- **依赖模块**: AD9959, ad_measure, my_fft
- **测试程序**: test/test_spectrum_analysis.c
- **触发方式**: 串口屏按键5
