# 快速多次扫频频谱分析功能说明

## 功能概述

本功能通过串口屏按键5触发，实现对未知滤波器的高精度频率响应特性分析。系统采用快速多次扫频技术，使用DDS产生扫频信号，经过待测滤波器后由AD高速采集，然后通过FFT分析得到滤波器的详细频率响应曲线和性能参数。

### 优化特性
- **快速扫频**: 每个频率点仅需10ms稳定时间
- **多次平均**: 3次扫频取平均值，提高测量精度
- **高分辨率**: 200个频率点，分辨率约500Hz
- **高速采样**: 2MHz采样频率，提升FFT分析精度

## 工作原理

### 信号链路
```
DDS扫频信号 → 未知滤波器 → AD采集 → FFT分析 → 频率响应特性
```

### 测试流程
1. **扫频信号生成**: DDS产生100Hz到100kHz的扫频信号
2. **信号调理**: 扫频信号经过待测的未知滤波器
3. **数据采集**: AD以1MHz采样率采集滤波器输出信号
4. **频谱分析**: 对每个频率点的采集数据进行FFT分析
5. **特性提取**: 计算滤波器的幅度响应和关键参数
6. **结果输出**: 通过串口输出详细的分析结果

## 技术参数

### 扫频参数 (优化版)
- **频率范围**: 100Hz - 100kHz
- **频率步数**: 201个频率点 (增加4倍精度)
- **步进大小**: 约500Hz (提高分辨率)
- **稳定时间**: 每个频率点10ms (快速扫频)
- **扫频次数**: 3次取平均值 (提高精度)

### 采集参数 (优化版)
- **采样频率**: 2MHz (提高到2倍)
- **采样点数**: 1024点（AD_FIFO_SIZE）
- **FFT长度**: 1024点
- **窗函数**: Hanning窗

### 分析参数 (优化版)
- **频率分辨率**: 约500Hz (扫频步进)
- **FFT分辨率**: 约1953Hz (2MHz/1024)
- **幅度精度**: 浮点数精度，多次平均
- **相对dB计算**: 20*log10(幅度/最大幅度)
- **总测试时间**: 约6秒 (3次 × 201点 × 10ms)

## 使用方法

### 1. 硬件连接
```
DDS输出 → 待测滤波器输入
待测滤波器输出 → AD输入通道1
```

### 2. 操作步骤
1. 确保硬件连接正确
2. 在串口屏上按下按键5
3. 系统自动开始扫频测试
4. 通过串口1查看实时测试进度和结果

### 3. 结果解读
系统会输出以下信息：
- 实时扫频进度
- 每个频率点的幅度响应
- 滤波器特性分析
- 详细的频率响应数据表

## 输出结果

### 实时进度信息 (优化版)
```
=== 开始快速多次扫频测试 ===
扫频范围: 100Hz - 100kHz
目标: 分析未知滤波器的频率响应
扫频次数: 3, 频率点数: 201

--- 第 1/3 次扫频 ---
扫频进度: 1/201 (100 Hz)
扫频进度: 11/201 (5100 Hz)
扫频进度: 21/201 (10100 Hz)
...
第 1 次扫频完成

--- 第 2/3 次扫频 ---
...
第 3 次扫频完成

计算 3 次扫频的平均值...
```

### 特性分析结果 (优化版)
```
=== 快速多次扫频完成，分析结果 ===
总测试点数: 201, 扫频次数: 3
频率分辨率: 500.0 Hz

采样统计 (每20个点显示一次):
频率(Hz)    平均幅度(V)    标准差估计
100         0.123456      0.001234
10100       0.234567      0.002345
20100       0.567890      0.001567
...

滤波器特性分析:
  最大响应: 0.987654 V @ 5000 Hz
  -3dB带宽: 3000 Hz (3500 Hz - 6500 Hz)

关键频率点:
最大响应点: 5000 Hz, 0.987654 V, 0.00 dB
-3dB下限: 3500 Hz, 0.698765 V, -3.01 dB
-3dB上限: 6500 Hz, 0.698234 V, -3.02 dB

滤波器类型分析:
  类型: 带通滤波器 (峰值在中频段)
  中心频率: 5000 Hz
  品质因数Q ≈ 1.67
  低频侧斜率: 18.5 dB/decade
  高频侧斜率: -22.3 dB/decade

性能指标:
  通带平坦度: ±0.15 dB
  阻带抑制: 45.2 dB
```

### 详细数据表
```
详细频率响应数据:
频率(Hz)    幅度(V)        相对dB
100         0.123456      -18.12
2100        0.234567      -12.45
4100        0.567890      -4.78
6100        0.987654       0.00
8100        0.456789      -6.89
...
```

## 滤波器类型识别

### 低通滤波器
- **特征**: 峰值在低频段（前1/3频率范围）
- **输出**: "可能是低通滤波器 (峰值在低频段)"

### 高通滤波器
- **特征**: 峰值在高频段（后1/3频率范围）
- **输出**: "可能是高通滤波器 (峰值在高频段)"

### 带通滤波器
- **特征**: 峰值在中频段
- **输出**: 中心频率和品质因数Q值

## 关键参数说明

### -3dB带宽
- **定义**: 幅度下降到最大值的70.7%（-3dB）时的频率范围
- **计算**: 上限频率 - 下限频率
- **意义**: 表示滤波器的有效通带宽度

### 品质因数Q
- **定义**: Q = 中心频率 / -3dB带宽
- **意义**: 表示滤波器的选择性，Q值越高选择性越好

### 相对dB
- **定义**: 20*log10(当前幅度/最大幅度)
- **意义**: 以最大响应为0dB基准的相对衰减

## 注意事项

### 1. 硬件要求
- DDS输出幅度应适合滤波器输入范围
- 滤波器输出应在AD输入范围内
- 确保信号链路阻抗匹配

### 2. 测试环境
- 避免外部干扰信号
- 确保电源稳定
- 保持温度稳定

### 3. 结果解释
- 结果仅反映当前测试条件下的特性
- 滤波器类型判断为自动识别，需结合实际情况
- 相位响应信息当前版本未实现

## 扩展功能

### 可扩展的功能
1. **相位响应分析**: 增加相位测量功能
2. **群延迟计算**: 计算滤波器的群延迟特性
3. **自动增益控制**: 根据信号幅度自动调整DDS输出
4. **数据导出**: 将测试结果保存到文件
5. **图形显示**: 在串口屏上显示频率响应曲线

### 参数可调
- 扫频范围可通过修改代码调整
- 频率步数可根据精度要求调整
- 稳定时间可根据滤波器特性调整

## 故障排除

### 1. 无响应或响应很小
- 检查DDS输出是否正常
- 检查滤波器连接
- 检查AD输入连接
- 调整DDS输出幅度

### 2. 结果异常
- 检查采样频率设置
- 检查FFT初始化
- 验证频率计算公式
- 检查数据缓冲区

### 3. 类型识别错误
- 手动分析频率响应数据
- 调整类型判断阈值
- 结合实际滤波器规格验证

## 开发者信息

- **模块**: hmi_key_handler.c/h
- **主要函数**: `Start_Spectrum_Analysis()`, `Analyze_Filter_Response()`
- **依赖模块**: AD9959, ad_measure, my_fft
- **测试程序**: test/test_spectrum_analysis.c
- **触发方式**: 串口屏按键5
