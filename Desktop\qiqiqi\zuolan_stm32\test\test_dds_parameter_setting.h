/**
 * @file test_dds_parameter_setting.h
 * @brief DDS参数设置功能测试程序头文件
 * @details 测试修正后的DDS参数设置功能，验证错误识别问题的解决
 * <AUTHOR>
 * @date 2025-07-30
 */

#ifndef __TEST_DDS_PARAMETER_SETTING_H__
#define __TEST_DDS_PARAMETER_SETTING_H__

#include "stdint.h"

// 函数声明

/**
 * @brief 模拟串口中断接收数据（逐字节接收）
 * @param hex_data 十六进制数据数组
 * @param len 数据长度
 */
void simulate_uart_interrupt_receive(uint8_t *hex_data, uint16_t len);

/**
 * @brief 模拟接收指定的十六进制数据（直接设置缓冲区）
 * @param hex_data 十六进制数据数组
 * @param len 数据长度
 */
void simulate_receive_data_direct(uint8_t *hex_data, uint16_t len);

/**
 * @brief 测试DDS参数设置功能（模拟真实串口中断）
 */
void test_dds_parameter_setting_interrupt(void);

/**
 * @brief 测试DDS参数设置功能（直接设置缓冲区）
 */
void test_dds_parameter_setting_direct(void);

/**
 * @brief 测试其他频率值
 */
void test_other_frequencies(void);

/**
 * @brief 主测试函数
 */
void run_dds_parameter_tests(void);

#endif // __TEST_DDS_PARAMETER_SETTING_H__
