/**
 * @file hmi_key_handler.c
 * @brief 串口屏按键处理模块
 * @details 该模块负责处理来自串口屏的按键数据，实现按键功能映射
 * <AUTHOR>
 * @date 2025-07-24
 */

#include "hmi_key_handler.h"
#include "my_usart.h"
#include "my_hmi.h"
#include "key_app.h"
#include "bsp_system.h"
#include "stdlib.h"  // 用于atoi函数
#include "stdio.h"   // 用于sprintf函数
#include "math.h"    // 用于log10f函数
#include "AD9959.h"  // 用于DDS扫频控制
#include "ad_measure.h" // 用于AD采集
#include "my_fft.h"  // 用于FFT频谱分析
#include "AD9959.h"  // 用于DDS扫频控制
#include "ad_measure.h" // 用于AD采集
#include "my_fft.h"  // 用于FFT频谱分析

// DDS控制相关变量
static uint8_t dds_output_enabled = 0;  // DDS输出使能状态：0=关闭，1=开启
static uint32_t dds_frequency = 1000; // DDS输出频率，默认1MHz
static uint8_t dds_channel = 1;          // DDS输出通道，默认通道1
static uint16_t dds_amplitude = 1023;    // DDS输出幅度，默认最大值
static float dds_phase = 0.0f;           // DDS输出相位，默认0度
static uint8_t dds_waveform_type = 0;    // DDS波形类型：0=正弦波，1=方波，2=三角波

// 串口屏参数设置相关变量
float freq_my = 1.0f;           // 频率参数，单位MHz
int vpp_my = 1000;              // 电压峰峰值参数，单位mV
static int temp_int = 0;        // 临时整数变量


// 波形类型字符串数组
static const char* waveform_names[] = {
    "SINE",     // 正弦波
    "SQUARE",   // 方波
    "TRIANGLE"  // 三角波
};

// DDS控制函数声明
static void DDS_Enable_Output(void);     // 开启DDS输出
static void DDS_Disable_Output(void);    // 关闭DDS输出
static void DDS_Increase_Frequency(void); // 增加频率100Hz
static void DDS_Decrease_Frequency(void); // 减少频率100Hz
static void DDS_Update_Display(void);    // 更新串口屏显示
static void Process_Parameter_Command(uint16_t len); // 处理参数设置命令
static void Process_Amplitude_Command(uint16_t len); // 处理幅度设置命令
static void Start_Spectrum_Analysis(void); // 开始频谱分析
static void Set_Frequency_1kHz(void);     // 设置频率为1kHz
static void Voltage_Step_Up(void);        // 电压步进0.1V
static void Voltage_Step_Down(void);      // 电压减0.1V

// 临时变量
static unsigned char len1; // 接收到的数据长度
static int i;              // 循环变量

/**
 * @brief 串口屏按键处理主函数
 * @details 检测串口屏按键数据并执行相应功能
 * @param None
 * @retval None
 */
void HMI_Key_Process(void)
{
    if (USART_RX_STA & 0x8000) // 检查是否接收到数据
    {
        uint16_t len = USART_RX_STA & 0x3fff; // 获取数据长度

        switch (USART_RX_BUF[0])
        {

					case 'A': // Frequency setting command: A + digits
            {
                Process_Parameter_Command(len);
                break;
            }

            case 'B': // Amplitude setting command: B + decimal
            {
                Process_Amplitude_Command(len);
                break;
            }
            case '1': // Key1: Enable DDS output
            {
                DDS_Enable_Output();
                my_printf(&huart1, "HMI Key1: DDS output enabled, freq=%luHz\r\n", dds_frequency);
                break;
            }

            case '2': // Key2: Pause DDS output
            {
                DDS_Disable_Output();
                my_printf(&huart1, "HMI Key2: DDS output paused\r\n");
                break;
            }

            case '3': // Key3: Increase 100Hz
            {
                DDS_Increase_Frequency();
                my_printf(&huart1, "HMI Key3: Freq +100Hz, current=%luHz\r\n", dds_frequency);
                break;
            }

            case '4': // Key4: Decrease 100Hz
            {
                DDS_Decrease_Frequency();
                my_printf(&huart1, "HMI Key4: Freq -100Hz, current=%luHz\r\n", dds_frequency);
                break;
            }



            case '5': // Key5: Spectrum Analysis with Frequency Sweep
            {
                Start_Spectrum_Analysis();
                my_printf(&huart1, "HMI Key5: Starting spectrum analysis (100Hz-100kHz)\r\n");
                break;
            }

            case '6': // Key6: Set frequency to 1kHz
            {
                Set_Frequency_1kHz();
                my_printf(&huart1, "HMI Key6: Frequency set to 1kHz\r\n");
                break;
            }

            case '8': // Key8: Voltage step up 0.1V
            {
                Voltage_Step_Up();
                my_printf(&huart1, "HMI Key8: Voltage +0.1V\r\n");
                break;
            }

            case '9': // Key9: Voltage step down 0.1V
            {
                Voltage_Step_Down();
                my_printf(&huart1, "HMI Key9: Voltage -0.1V\r\n");
                break;
            }

            default:
            {
                my_printf(&huart1, "Unknown HMI key: %c\r\n", USART_RX_BUF[0]);
                break;
            }
        }

        USART_RX_STA = 0; // Clear receive status flag
    }
}


/**
 * @brief 获取当前按键状态
 * @details 返回当前按下的按键值
 * @param None
 * @retval uint8_t 按键值('1'-'5')，无按键返回0
 */
uint8_t HMI_Get_Key_Status(void)
{
    if (USART_RX_STA & 0x8000)
    {
        return USART_RX_BUF[0];
    }
    return 0;
}

/**
 * @brief 清除按键状态
 * @details 清除当前按键接收状态
 * @param None
 * @retval None
 */
void HMI_Clear_Key_Status(void)
{
    USART_RX_STA = 0;
}

// ==================== DDS控制函数实现 ====================

/**
 * @brief 开启DDS输出
 * @details 启用DDS输出并更新串口屏显示
 */
static void DDS_Enable_Output(void)
{
    dds_output_enabled = 1;

    // 配置DDS输出
    AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);

    // 更新串口屏显示
    DDS_Update_Display();

}

/**
 * @brief 关闭DDS输出
 * @details 禁用DDS输出并更新串口屏显示
 */
static void DDS_Disable_Output(void)
{
    dds_output_enabled = 0;

    // 关闭DDS输出（设置幅度为0）
    AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, 0);

    // 更新串口屏显示
    DDS_Update_Display();

}

/**
 * @brief 增加DDS频率100Hz
 * @details 将当前频率增加100Hz，如果输出已开启则立即应用
 */
static void DDS_Increase_Frequency(void)
{
    dds_frequency += 100; // 增加100Hz

    // 频率上限检查（最大50MHz）
    if (dds_frequency > 50000000) {
        dds_frequency = 50000000;
    }

    // 如果DDS输出已开启，立即应用新频率
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 减少DDS频率100Hz
 * @details 将当前频率减少100Hz，如果输出已开启则立即应用
 */
static void DDS_Decrease_Frequency(void)
{
    // 频率下限检查（最小100Hz）
    if (dds_frequency > 100) {
        dds_frequency -= 100; // 减少100Hz
    }

    // 如果DDS输出已开启，立即应用新频率
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 更新串口屏显示
 * @details 将当前DDS频率、波形类型和幅度分别显示到t4、t5、t6控件
 */
static void DDS_Update_Display(void)
{
    char freq_str[32];
    char amp_str[16];
//    char status_str[32];

    // 1. 格式化频率字符串并显示到t4控件
    if (dds_frequency >= 1000000) {
        sprintf(freq_str, "%.2fMHz", dds_frequency / 1000000.0f);
    } else if (dds_frequency >= 1000) {
        sprintf(freq_str, "%.2fkHz", dds_frequency / 1000.0f);
    } else {
        sprintf(freq_str, "%luHz", dds_frequency);
    }
    HMI_Send_String(huart2, "t4", freq_str); // t4：频率

    // 2. 显示波形类型到t5控件
    const char* waveform_name = waveform_names[dds_waveform_type];
    HMI_Send_String(huart2, "t5", (char*)waveform_name); // t5：波形

    // 3. 格式化幅度字符串并显示到t6控件
    if (dds_output_enabled) {
        // 将幅度值转换为百分比显示（0-1023 -> 0-100%）
        uint8_t amp_percent = (dds_amplitude * 100) / 1023;
        sprintf(amp_str, "%d%%", amp_percent);
    } else {
        sprintf(amp_str, "OFF");
    }
    HMI_Send_String(huart2, "t6", amp_str); // t6：幅度
}

// ==================== 公共接口函数 ====================

/**
 * @brief 获取DDS输出状态
 * @details 返回当前DDS输出是否开启
 * @param None
 * @retval uint8_t 1=开启，0=关闭
 */
uint8_t HMI_Get_DDS_Status(void)
{
    return dds_output_enabled;
}

/**
 * @brief 获取DDS当前频率
 * @details 返回当前设置的DDS频率值
 * @param None
 * @retval uint32_t 频率值（Hz）
 */
uint32_t HMI_Get_DDS_Frequency(void)
{
    return dds_frequency;
}

/**
 * @brief 设置DDS频率
 * @details 直接设置DDS频率值
 * @param frequency 频率值（Hz）
 * @retval None
 */
void HMI_Set_DDS_Frequency(uint32_t frequency)
{
    // 频率范围检查
    if (frequency < 100) {
        frequency = 100; // 最小100Hz
    } else if (frequency > 50000000) {
        frequency = 50000000; // 最大50MHz
    }

    dds_frequency = frequency;

    // 如果DDS输出已开启，立即应用新频率
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 获取DDS波形类型
 * @details 返回当前设置的DDS波形类型
 * @param None
 * @retval uint8_t 波形类型（0=正弦波，1=方波，2=三角波）
 */
uint8_t HMI_Get_DDS_Waveform(void)
{
    return dds_waveform_type;
}

/**
 * @brief 设置DDS波形类型
 * @details 设置DDS输出的波形类型
 * @param waveform 波形类型（0=正弦波，1=方波，2=三角波）
 * @retval None
 */
void HMI_Set_DDS_Waveform(uint8_t waveform)
{
    // 波形类型范围检查
    if (waveform > 2) {
        waveform = 0; // 默认为正弦波
    }

    dds_waveform_type = waveform;

    // 注意：AD9959主要输出正弦波，这里的波形类型主要用于显示
    // 实际的波形控制可能需要额外的硬件配置

    // 如果DDS输出已开启，立即应用新设置
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 获取DDS幅度
 * @details 返回当前设置的DDS幅度值
 * @param None
 * @retval uint16_t 幅度值（0-1023）
 */
uint16_t HMI_Get_DDS_Amplitude(void)
{
    return dds_amplitude;
}

/**
 * @brief 设置DDS幅度
 * @details 设置DDS输出的幅度值
 * @param amplitude 幅度值（0-1023）
 * @retval None
 */
void HMI_Set_DDS_Amplitude(uint16_t amplitude)
{
    // 幅度范围检查
    if (amplitude > 1023) {
        amplitude = 1023; // 最大值限制
    }

    dds_amplitude = amplitude;

    // 如果DDS输出已开启，立即应用新幅度
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 初始化DDS控制显示
 * @details 初始化串口屏上的DDS状态显示
 * @param None
 * @retval None
 */
void HMI_DDS_Display_Init(void)
{
    // 初始化时更新一次显示，确保串口屏显示当前DDS状态
    DDS_Update_Display();

    // 发送初始化完成信息
    HMI_Send_String(huart2, "t0", "DDS Ready");

    my_printf(&huart1, "DDS控制显示初始化完成\r\n");
    my_printf(&huart1, "t4: 频率显示, t5: 波形显示, t6: 幅度显示\r\n");
}

/**
 * @brief DDS状态实时更新任务
 * @details 定期更新串口屏上的DDS状态信息，用于调度器调用
 * @param None
 * @retval None
 */
void HMI_DDS_Display_Update(void)
{
    // 调用内部显示更新函数
    DDS_Update_Display();
}

/**
 * @brief Process parameter setting command
 * @details Parse frequency setting command starting with A, format: A123\r\n means set freq to 123Hz
 * @param len Length of received data
 * @retval None
 */
static void Process_Parameter_Command(uint16_t len)
{
    len1 = len; // Save data length
    temp_int = 0;

    // Extract digits: get all digits after 'A'
    // Data format: A123\r\n (means frequency 123Hz)
    char freq_str[20] = {0};
    int freq_index = 0;

    // Extract digits from position 1 (skip 'A')
    for (i = 1; i < len1; i++) {
        if (USART_RX_BUF[i] >= '0' && USART_RX_BUF[i] <= '9') {
            freq_str[freq_index++] = USART_RX_BUF[i];
        }
        // 遇到回车换行符停止解析
        else if (USART_RX_BUF[i] == '\r' || USART_RX_BUF[i] == '\n') {
            break;
        }
    }
    freq_str[freq_index] = '\0'; // String terminator


    if (freq_index > 0) {
        // Convert to integer (unit: Hz)
        temp_int = atoi(freq_str);
        dds_frequency = (uint32_t)temp_int;
        // Frequency range check
        if (dds_frequency < 100) {
            dds_frequency = 100; // Min 100Hz
        } else if (dds_frequency > 50000000) {
            dds_frequency = 50000000; // Max 50MHz
        }

        // Apply to DDS hardware immediately
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);

        // Update HMI display
        DDS_Update_Display();

        // Update freq_my variable (convert to MHz for display)
       freq_my = (float)dds_frequency / 1000000.0f;

    }
}

/**
 * @brief Process amplitude setting command
 * @details Parse amplitude setting command starting with B, format: B2.5\r\n means set amplitude to 2.5V
 * @param len Length of received data
 * @retval None
 */
static void Process_Amplitude_Command(uint16_t len)
{
    len1 = len; // Save data length
    float amplitude_value = 0.0f;

    // Extract decimal number: get all digits and decimal point after 'B'
    // Data format: B2.5\r\n (means amplitude 2.5V)
    char amplitude_str[20] = {0};
    int amplitude_index = 0;
    for (i = 0; i < len1; i++) {
        my_printf(&huart1, "%02X ", USART_RX_BUF[i]); // 显示十六进制数据
    }
    for (i = 0; i < len1; i++) {
        if (USART_RX_BUF[i] >= 0x20 && USART_RX_BUF[i] <= 0x7E) { // 可打印字符
            my_printf(&huart1, "%c", USART_RX_BUF[i]);
        } else {
            my_printf(&huart1, "[%02X]", USART_RX_BUF[i]); // 不可打印字符显示十六进制
        }
    }
    my_printf(&huart1, "\r\n");

    // Extract digits and decimal point from position 1 (skip 'B')
    for (i = 1; i < len1; i++) {
        if ((USART_RX_BUF[i] >= '0' && USART_RX_BUF[i] <= '9') || USART_RX_BUF[i] == '.') {
            amplitude_str[amplitude_index++] = USART_RX_BUF[i];
        }
        // 遇到回车换行符停止解析
        else if (USART_RX_BUF[i] == '\r' || USART_RX_BUF[i] == '\n') {
            break;
        }
    }
    amplitude_str[amplitude_index] = '\0'; // String terminator

    if (amplitude_index > 0) {
        // Convert to float (unit: V)
        amplitude_value = atof(amplitude_str);

        // Amplitude range check (0~3V)
        if (amplitude_value < 0.0f) {
            amplitude_value = 0.0f; // Min 0V
        } else if (amplitude_value > 0.55f) {
            amplitude_value = 0.55f; // Max 3V
        }

        // Convert voltage to DAC value (0V=0, 3V=1023)
        // DAC范围: 0-1023, 对应电压: 0-3V
        uint16_t dac_value = (uint16_t)(amplitude_value * 1023.0f / 0.55f);
        dds_amplitude = dac_value;
        // Apply to DDS hardware immediately
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
        // Update HMI display
        DDS_Update_Display();

        // Update vpp_my variable (convert to mV for display)
        vpp_my = (int)(amplitude_value * 1000.0f);
    } else {
        my_printf(&huart1, "Amplitude setting error: no valid number found\r\n");
        my_printf(&huart1, "Correct format: B2.5 (set amplitude to 2.5V)\r\n");
    }
}

// 函数声明
static void Analyze_Filter_Response(float *freq_points, float *magnitude, uint32_t num_points);

/**
 * @brief 开始频谱分析（扫频测试滤波器响应）
 * @details 通过DDS产生100Hz到100kHz的扫频信号，经过未知滤波器后用AD采集，
 *          然后进行FFT分析得到滤波器的频率响应特性
 * @param None
 * @retval None
 */
static void Start_Spectrum_Analysis(void)
{
    my_printf(&huart1, "\r\n=== 开始频谱分析 ===\r\n");
    my_printf(&huart1, "扫频范围: 100Hz - 100kHz\r\n");
    my_printf(&huart1, "目标: 分析未知滤波器的频率响应\r\n\r\n");

    // 频谱分析参数 - 快速多次扫频优化
    const uint32_t start_freq = 100;      // 起始频率 100Hz
    const uint32_t end_freq = 100000;     // 结束频率 100kHz
    const uint32_t freq_steps = 200;      // 增加频率步数到200点
    const uint32_t freq_step_size = (end_freq - start_freq) / freq_steps; // 每步约500Hz
    const uint32_t settle_time_ms = 10;   // 减少稳定时间到10ms (快速扫频)
    const uint32_t sample_freq = 2000000; // 提高AD采样频率到2MHz
    const uint32_t sweep_count = 3;       // 多次扫频取平均值

    // 存储频率响应数据 - 增加数组大小
    static float frequency_points[201];    // 频率点 (200+1)
    static float magnitude_response[201];  // 幅度响应
    static float magnitude_sum[201];       // 多次扫频累加值
    static float magnitude_avg[201];       // 平均幅度响应

    // 初始化FFT模块
    fft_init();

    // 保存原始DDS设置
    uint32_t original_freq = HMI_Get_DDS_Frequency();
    uint8_t original_status = HMI_Get_DDS_Status();

    // 确保DDS输出开启
    if (!original_status) {
        DDS_Enable_Output();
        HAL_Delay(10); // 等待DDS稳定
    }

    my_printf(&huart1, "开始快速多次扫频测试...\r\n");
    my_printf(&huart1, "扫频次数: %lu, 频率点数: %lu\r\n", sweep_count, freq_steps+1);

    // 初始化累加数组
    for (uint32_t i = 0; i <= freq_steps; i++) {
        magnitude_sum[i] = 0.0f;
        frequency_points[i] = (float)(start_freq + i * freq_step_size);
    }

    // 多次扫频循环
    for (uint32_t sweep = 0; sweep < sweep_count; sweep++) {
        my_printf(&huart1, "\r\n--- 第 %lu/%lu 次扫频 ---\r\n", sweep+1, sweep_count);

        // 单次扫频循环
        for (uint32_t step = 0; step <= freq_steps; step++) {
            uint32_t current_freq = start_freq + step * freq_step_size;

            // 每10个点显示一次进度
            if (step % 10 == 0 || step == freq_steps) {
                my_printf(&huart1, "扫频进度: %lu/%lu (%.0f Hz)\r\n",
                         step+1, freq_steps+1, frequency_points[step]);
            }

            // 设置DDS输出频率
            HMI_Set_DDS_Frequency(current_freq);
            HAL_Delay(settle_time_ms); // 快速稳定时间

            // AD采集数据（经过滤波器后的信号）
            vpp_adc_parallel((float)sample_freq, FREQ_MODE_SAMPLING, 0, FREQ_MODE_SAMPLING);

            // 对采集的数据进行FFT分析
            extern float fifo_data1_f[]; // AD1采集的浮点数据
            calculate_fft_spectrum(fifo_data1_f, AD_FIFO_SIZE);

            // 计算当前频率点的幅度响应
            // 找到FFT结果中对应当前频率的bin
            extern float fft_magnitude[];
            uint32_t target_bin = (uint32_t)((float)current_freq * FFT_LENGTH / sample_freq);
            if (target_bin >= FFT_LENGTH/2) target_bin = FFT_LENGTH/2 - 1;

            // 累加幅度响应值
            magnitude_sum[step] += fft_magnitude[target_bin];
        }

        my_printf(&huart1, "第 %lu 次扫频完成\r\n", sweep+1);
    }

    // 计算平均值
    my_printf(&huart1, "\r\n计算 %lu 次扫频的平均值...\r\n", sweep_count);
    for (uint32_t step = 0; step <= freq_steps; step++) {
        magnitude_avg[step] = magnitude_sum[step] / (float)sweep_count;
        magnitude_response[step] = magnitude_avg[step]; // 用于后续分析
    }

    my_printf(&huart1, "\r\n=== 快速多次扫频完成，分析结果 ===\r\n");
    my_printf(&huart1, "总测试点数: %lu, 扫频次数: %lu\r\n", freq_steps+1, sweep_count);
    my_printf(&huart1, "频率分辨率: %.1f Hz\r\n", (float)freq_step_size);

    // 输出部分采样点的统计信息
    my_printf(&huart1, "\r\n采样统计 (每20个点显示一次):\r\n");
    my_printf(&huart1, "频率(Hz)\t平均幅度(V)\t标准差估计\r\n");
    for (uint32_t i = 0; i <= freq_steps; i += 20) {
        // 简单的标准差估计（基于相邻点的变化）
        float variance_est = 0.0f;
        if (i > 0 && i < freq_steps) {
            float diff1 = magnitude_avg[i] - magnitude_avg[i-1];
            float diff2 = magnitude_avg[i+1] - magnitude_avg[i];
            variance_est = (diff1*diff1 + diff2*diff2) / 2.0f;
        }
        my_printf(&huart1, "%.0f\t\t%.6f\t\t%.6f\r\n",
                 frequency_points[i], magnitude_avg[i], sqrtf(variance_est));
    }

    // 分析并输出频率响应特性
    Analyze_Filter_Response(frequency_points, magnitude_response, freq_steps + 1);

    // 恢复原始DDS设置
    HMI_Set_DDS_Frequency(original_freq);
    if (!original_status) {
        DDS_Disable_Output();
    }

    my_printf(&huart1, "频谱分析完成！\r\n");
}

/**
 * @brief 分析滤波器频率响应特性
 * @param freq_points 频率点数组
 * @param magnitude 幅度响应数组
 * @param num_points 数据点数量
 * @retval None
 */
static void Analyze_Filter_Response(float *freq_points, float *magnitude, uint32_t num_points)
{
    if (num_points == 0) return;

    // 找到最大幅度和对应频率
    float max_magnitude = magnitude[0];
    float max_freq = freq_points[0];
    uint32_t max_index = 0;

    for (uint32_t i = 1; i < num_points; i++) {
        if (magnitude[i] > max_magnitude) {
            max_magnitude = magnitude[i];
            max_freq = freq_points[i];
            max_index = i;
        }
    }

    my_printf(&huart1, "滤波器特性分析:\r\n");
    my_printf(&huart1, "  最大响应: %.6f V @ %.0f Hz\r\n", max_magnitude, max_freq);

    // 计算-3dB带宽
    float threshold_3db = max_magnitude * 0.707f; // -3dB = 1/√2
    uint32_t lower_3db = 0, upper_3db = num_points - 1;

    // 找下限频率
    for (uint32_t i = 0; i < max_index; i++) {
        if (magnitude[i] >= threshold_3db) {
            lower_3db = i;
            break;
        }
    }

    // 找上限频率
    for (uint32_t i = max_index; i < num_points; i++) {
        if (magnitude[i] < threshold_3db) {
            upper_3db = i - 1;
            break;
        }
    }

    float bandwidth = freq_points[upper_3db] - freq_points[lower_3db];
    my_printf(&huart1, "  -3dB带宽: %.0f Hz (%.0f Hz - %.0f Hz)\r\n",
             bandwidth, freq_points[lower_3db], freq_points[upper_3db]);

    // 输出详细的频率响应数据 (每10个点显示一次，避免输出过多)
    my_printf(&huart1, "\r\n详细频率响应数据 (每10个点):\r\n");
    my_printf(&huart1, "频率(Hz)\t幅度(V)\t\t相对dB\r\n");

    uint32_t display_step = (num_points > 50) ? 10 : 1; // 数据点多时每10个显示一次
    for (uint32_t i = 0; i < num_points; i += display_step) {
        float relative_db = 20.0f * log10f(magnitude[i] / max_magnitude);
        my_printf(&huart1, "%.0f\t\t%.6f\t%.2f\r\n",
                 freq_points[i], magnitude[i], relative_db);
    }

    // 额外显示关键频率点
    my_printf(&huart1, "\r\n关键频率点:\r\n");
    my_printf(&huart1, "最大响应点: %.0f Hz, %.6f V, 0.00 dB\r\n",
             max_freq, max_magnitude);
    if (lower_3db < num_points && upper_3db < num_points) {
        my_printf(&huart1, "-3dB下限: %.0f Hz, %.6f V, %.2f dB\r\n",
                 freq_points[lower_3db], magnitude[lower_3db],
                 20.0f * log10f(magnitude[lower_3db] / max_magnitude));
        my_printf(&huart1, "-3dB上限: %.0f Hz, %.6f V, %.2f dB\r\n",
                 freq_points[upper_3db], magnitude[upper_3db],
                 20.0f * log10f(magnitude[upper_3db] / max_magnitude));
    }

    // 滤波器类型判断
    my_printf(&huart1, "\r\n滤波器类型分析:\r\n");

    // 检查是否为低通滤波器
    if (max_index < num_points / 3) {
        my_printf(&huart1, "  可能是低通滤波器 (峰值在低频段)\r\n");
    }
    // 检查是否为高通滤波器
    else if (max_index > num_points * 2 / 3) {
        my_printf(&huart1, "  可能是高通滤波器 (峰值在高频段)\r\n");
    }
    // 检查是否为带通滤波器
    else {
        my_printf(&huart1, "  可能是带通滤波器 (峰值在中频段)\r\n");
        my_printf(&huart1, "  中心频率: %.0f Hz\r\n", max_freq);
        my_printf(&huart1, "  品质因数Q ≈ %.2f\r\n", max_freq / bandwidth);
    }
}

/**
 * @brief 设置频率为1kHz
 * @details 快速设置DDS输出频率为1000Hz，常用的测试频率
 * @param None
 * @retval None
 */
static void Set_Frequency_1kHz(void)
{
    const uint32_t target_freq = 1000; // 1kHz

    // 设置DDS频率
    dds_frequency = target_freq;

    // 如果DDS输出已开启，立即应用新频率
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    my_printf(&huart1, "频率已设置为: %lu Hz (1kHz)\r\n", dds_frequency);

    // 更新HMI显示
    DDS_Update_Display();

    // 更新freq_my变量 (转换为MHz显示)
    freq_my = (float)dds_frequency / 1000000.0f;
}

/**
 * @brief 电压步进0.1V
 * @details 将DDS输出幅度增加0.1V，范围限制在0-3V
 * @param None
 * @retval None
 */
static void Voltage_Step_Up(void)
{
    const float voltage_step = 0.1f; // 0.1V步进

    // 获取当前电压值 (从DAC值转换)
    float current_voltage = (float)dds_amplitude * 3.0f / 1023.0f;

    // 增加0.1V
    float new_voltage = current_voltage + voltage_step;

    // 电压范围限制 (0-3V)
    if (new_voltage > 3.0f) {
        new_voltage = 3.0f;
        my_printf(&huart1, "电压已达到最大值3.0V\r\n");
    }

    // 转换为DAC值 (0V=0, 3V=1023)
    uint16_t new_dac_value = (uint16_t)(new_voltage * 1023.0f / 3.0f);
    dds_amplitude = new_dac_value;

    // 如果DDS输出已开启，立即应用新幅度
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    my_printf(&huart1, "电压步进: %.2fV -> %.2fV (DAC: %u)\r\n",
             current_voltage, new_voltage, new_dac_value);

    // 更新HMI显示
    DDS_Update_Display();

    // 更新vpp_my变量 (转换为mV显示)
    vpp_my = (int)(new_voltage * 1000.0f);
}

/**
 * @brief 电压步减0.1V
 * @details 将DDS输出幅度减少0.1V，范围限制在0-3V
 * @param None
 * @retval None
 */
static void Voltage_Step_Down(void)
{
    const float voltage_step = 0.1f; // 0.1V步进

    // 获取当前电压值 (从DAC值转换)
    float current_voltage = (float)dds_amplitude * 3.0f / 1023.0f;

    // 减少0.1V
    float new_voltage = current_voltage - voltage_step;

    // 电压范围限制 (0-3V)
    if (new_voltage < 0.0f) {
        new_voltage = 0.0f;
        my_printf(&huart1, "电压已达到最小值0.0V\r\n");
    }

    // 转换为DAC值 (0V=0, 3V=1023)
    uint16_t new_dac_value = (uint16_t)(new_voltage * 1023.0f / 3.0f);
    dds_amplitude = new_dac_value;

    // 如果DDS输出已开启，立即应用新幅度
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    my_printf(&huart1, "电压步减: %.2fV -> %.2fV (DAC: %u)\r\n",
             current_voltage, new_voltage, new_dac_value);

    // 更新HMI显示
    DDS_Update_Display();

    // 更新vpp_my变量 (转换为mV显示)
    vpp_my = (int)(new_voltage * 1000.0f);
}
