/**
 * @file test_spectrum_analysis.h
 * @brief 频谱分析功能测试程序头文件
 * @details 测试按键5触发的频谱分析功能的相关声明
 * <AUTHOR>
 * @date 2025-07-30
 */

#ifndef __TEST_SPECTRUM_ANALYSIS_H__
#define __TEST_SPECTRUM_ANALYSIS_H__

#include "stdint.h"

// 函数声明

/**
 * @brief 模拟按键5按下
 */
void simulate_key5_press(void);

/**
 * @brief 测试频谱分析功能
 */
void test_spectrum_analysis_function(void);

/**
 * @brief 测试滤波器响应分析算法
 */
void test_filter_analysis_algorithm(void);

/**
 * @brief 测试扫频参数计算
 */
void test_sweep_parameters(void);

/**
 * @brief 主测试函数
 */
void run_spectrum_analysis_tests(void);

#endif // __TEST_SPECTRUM_ANALYSIS_H__
