# 频谱分析功能开发报告

## 开发概述

**日期**: 2025-07-30  
**功能**: 按键5触发的频谱分析功能  
**目标**: 通过扫频测试分析未知滤波器的频率响应特性  

## 功能需求

### 核心需求
1. **触发方式**: 串口屏按键5按下后启动
2. **扫频范围**: 100Hz到100kHz
3. **信号链路**: DDS扫频信号 → 未知滤波器 → AD采集
4. **分析方法**: 对AD采集数据进行FFT频谱分析
5. **输出结果**: 滤波器频率响应特性和类型识别

### 技术要求
- 实时扫频控制
- 高精度AD采集
- FFT频谱分析
- 自动滤波器类型识别
- 详细的测试报告输出

## 实现方案

### 1. 系统架构
```
按键5触发 → 扫频控制 → 信号调理 → AD采集 → FFT分析 → 结果输出
     ↓           ↓           ↓          ↓         ↓         ↓
  HMI按键    DDS控制    未知滤波器   AD采样    频谱计算   特性分析
```

### 2. 核心模块

#### 2.1 按键处理模块
**文件**: `MY_Communication/Src/hmi_key_handler.c`
**函数**: `HMI_Key_Process()` - case '5'
**功能**: 检测按键5并触发频谱分析

#### 2.2 频谱分析主控模块
**函数**: `Start_Spectrum_Analysis()`
**功能**: 
- 扫频参数配置
- 扫频循环控制
- 数据采集协调
- 结果分析调用

#### 2.3 滤波器特性分析模块
**函数**: `Analyze_Filter_Response()`
**功能**:
- 频率响应数据分析
- 滤波器类型识别
- 关键参数计算
- 结果格式化输出

### 3. 技术参数

#### 3.1 扫频参数
```c
const uint32_t start_freq = 100;      // 起始频率 100Hz
const uint32_t end_freq = 100000;     // 结束频率 100kHz
const uint32_t freq_steps = 50;       // 频率步数
const uint32_t freq_step_size = (end_freq - start_freq) / freq_steps; // 约2kHz
const uint32_t settle_time_ms = 50;   // 每个频率点的稳定时间
```

#### 3.2 采集参数
```c
const uint32_t sample_freq = 1000000; // AD采样频率 1MHz
// FFT参数由my_fft.h定义
// 采样点数: AD_FIFO_SIZE (1024点)
// FFT长度: FFT_LENGTH (1024点)
```

#### 3.3 分析参数
- **频率分辨率**: 约977Hz (1MHz/1024)
- **幅度精度**: 浮点数精度
- **相对dB计算**: 20*log10(幅度/最大幅度)

## 代码实现

### 1. 主要函数

#### 1.1 Start_Spectrum_Analysis()
```c
static void Start_Spectrum_Analysis(void)
{
    // 1. 参数初始化
    // 2. 保存原始DDS设置
    // 3. 扫频循环
    for (uint32_t step = 0; step <= freq_steps; step++) {
        // 设置DDS频率
        // 等待信号稳定
        // AD采集数据
        // FFT分析
        // 记录幅度响应
    }
    // 4. 调用结果分析
    // 5. 恢复原始设置
}
```

#### 1.2 Analyze_Filter_Response()
```c
static void Analyze_Filter_Response(float *freq_points, float *magnitude, uint32_t num_points)
{
    // 1. 找到最大响应点
    // 2. 计算-3dB带宽
    // 3. 滤波器类型判断
    // 4. 输出详细数据表
    // 5. 输出分析结论
}
```

### 2. 关键算法

#### 2.1 FFT频率bin计算
```c
uint32_t target_bin = (uint32_t)((float)current_freq * FFT_LENGTH / sample_freq);
if (target_bin >= FFT_LENGTH/2) target_bin = FFT_LENGTH/2 - 1;
magnitude_response[step] = fft_magnitude[target_bin];
```

#### 2.2 -3dB带宽计算
```c
float threshold_3db = max_magnitude * 0.707f; // -3dB = 1/√2
// 找下限和上限频率
float bandwidth = freq_points[upper_3db] - freq_points[lower_3db];
```

#### 2.3 滤波器类型识别
```c
if (max_index < num_points / 3) {
    // 低通滤波器
} else if (max_index > num_points * 2 / 3) {
    // 高通滤波器
} else {
    // 带通滤波器
    float Q = max_freq / bandwidth; // 品质因数
}
```

## 测试验证

### 1. 测试程序
**文件**: `test/test_spectrum_analysis.c`
**功能**: 
- 模拟按键5按下
- 验证扫频参数计算
- 测试滤波器分析算法
- 完整功能测试

### 2. 测试用例
1. **扫频参数测试**: 验证频率点计算正确性
2. **按键触发测试**: 验证按键5能正确启动功能
3. **状态恢复测试**: 验证DDS设置能正确恢复
4. **算法测试**: 验证滤波器类型识别算法

## 输出结果

### 1. 实时进度信息
```
=== 开始频谱分析 ===
扫频范围: 100Hz - 100kHz
目标: 分析未知滤波器的频率响应

开始扫频测试...
测试频率: 100 Hz (1/51)
  -> 幅度响应: 0.123456 V (FFT bin 0)
```

### 2. 特性分析结果
```
滤波器特性分析:
  最大响应: 0.987654 V @ 5000 Hz
  -3dB带宽: 3000 Hz (3500 Hz - 6500 Hz)

滤波器类型分析:
  可能是带通滤波器 (峰值在中频段)
  中心频率: 5000 Hz
  品质因数Q ≈ 1.67
```

### 3. 详细数据表
```
详细频率响应数据:
频率(Hz)    幅度(V)        相对dB
100         0.123456      -18.12
2100        0.234567      -12.45
4100        0.567890      -4.78
6100        0.987654       0.00
```

## 集成说明

### 1. 文件修改
- `MY_Communication/Src/hmi_key_handler.c`: 主要实现
- `MY_Communication/Inc/hmi_key_handler.h`: 头文件更新
- `MY_Communication/Src/my_usart.c`: 按键B支持(幅度设置)

### 2. 依赖模块
- `AD9959.h`: DDS控制
- `ad_measure.h`: AD采集
- `my_fft.h`: FFT分析
- `math.h`: 数学函数

### 3. 调度器集成
功能通过现有的HMI按键处理任务自动集成，无需额外配置。

## 使用指南

### 1. 硬件连接
```
DDS输出 → 待测滤波器输入
待测滤波器输出 → AD输入通道1
```

### 2. 操作步骤
1. 确保硬件连接正确
2. 在串口屏上按下按键5
3. 通过串口1观察测试进度
4. 等待测试完成并查看结果

### 3. 结果解读
- **最大响应**: 滤波器的峰值响应和对应频率
- **-3dB带宽**: 滤波器的有效通带宽度
- **滤波器类型**: 自动识别为低通、高通或带通
- **品质因数Q**: 带通滤波器的选择性指标

## 扩展功能

### 1. 可扩展项
- 相位响应分析
- 群延迟计算
- 自动增益控制
- 数据导出功能
- 图形显示

### 2. 参数可调
- 扫频范围可通过修改常量调整
- 频率步数可根据精度要求调整
- 稳定时间可根据滤波器特性调整

## 总结

成功实现了按键5触发的频谱分析功能，能够：

1. **自动扫频**: 从100Hz到100kHz自动扫频测试
2. **实时分析**: 对每个频率点进行FFT分析
3. **智能识别**: 自动识别滤波器类型和关键参数
4. **详细输出**: 提供完整的测试报告和数据表
5. **状态恢复**: 测试完成后自动恢复原始DDS设置

该功能为未知滤波器的特性分析提供了强大的工具，具有良好的扩展性和实用性。

**开发完成文件列表**:
1. `MY_Communication/Src/hmi_key_handler.c` - 主要实现
2. `MY_Communication/Inc/hmi_key_handler.h` - 头文件更新
3. `test/test_spectrum_analysis.c` - 测试程序
4. `test/test_spectrum_analysis.h` - 测试头文件
5. `频谱分析功能说明.md` - 详细说明文档
6. `频谱分析功能开发报告.md` - 开发报告
7. `串口屏按键使用指南.md` - 更新使用指南
