# 串口屏DDS频率设置功能说明

## 功能概述

本功能实现了通过串口屏发送简化命令来直接控制DDS模块的频率设置。支持实时频率调整，命令格式简洁易用。

## 命令格式

### 基本格式
```
A + 数字序列 + \r\n
```

### 频率设置命令

#### 格式说明
- **格式**: `A` + 数字 + `\r\n`
- **单位**: Hz (赫兹)
- **数据格式**: 十六进制数据以回车换行结束
- **示例**:
  - `A123\r\n` → 设置频率为123Hz
  - 十六进制: `41 31 32 33 0D 0A` → 设置频率为123Hz
  - `A1000\r\n` → 设置频率为1000Hz (1kHz)
  - `A1000000\r\n` → 设置频率为1000000Hz (1MHz)
- **范围**: 100Hz - 50MHz

#### 数据解析规则
- 系统会自动提取'A'后面的所有数字字符
- 遇到回车符(\r)或换行符(\n)停止解析
- 支持连续数字格式，以回车换行结束

#### 修正说明 (2025-07-30)
- **问题**: 原程序无法正确处理多字符参数设置命令
- **修正**: 更新串口接收逻辑，支持多字符命令接收
- **测试数据**: `41 31 32 33 0D 0A` (A123\r\n) 正确设置为123Hz

## 使用方法

### 1. 硬件连接
- 确保串口屏连接到USART2
- 确保AD9959 DDS模块正确连接

### 2. 软件配置
确保在main函数中已完成以下初始化：
```c
HAL_UART_Receive_IT(&huart2, &rxTemp2, 1); // 启动串口2中断接收
AD9959_Init();                              // 初始化AD9959芯片
scheduler_init();                           // 初始化调度器
```

### 3. 发送命令
通过串口屏发送命令，系统会自动解析并应用到DDS硬件。

## 命令示例

### 基本频率设置
```
A123\r\n      → 设置频率为123Hz
A1000\r\n     → 设置频率为1000Hz (1kHz)
A5000\r\n     → 设置频率为5000Hz (5kHz)
```

### 十六进制数据示例
```
41 31 32 33 0D 0A  → A123\r\n → 123Hz
41 31 30 30 30 0D 0A → A1000\r\n → 1000Hz
41 35 30 30 30 0D 0A → A5000\r\n → 5000Hz
```

### 常用频率设置
```
A1000000\r\n  → 1MHz (1000000Hz)
A2000000\r\n  → 2MHz (2000000Hz)
A5000000\r\n  → 5MHz (5000000Hz)
A10000000\r\n → 10MHz (10000000Hz)
```

### 配合按键使用
```
1. A1000000\r\n  → 设置频率为1MHz
2. 1             → 开启DDS输出（按键1）
3. A2000000\r\n  → 调整频率为2MHz
4. 2             → 暂停DDS输出（按键2）
```

## 反馈信息

系统会通过USART1返回详细的执行信息：

### 成功设置示例
```
Received freq cmd, raw data (hex): 41 31 32 33 0D 0A
Received freq cmd, ASCII: A123[0D][0A]
Extracted freq digits: 123
Set frequency: 123 Hz
DDS frequency set to: 123 Hz
```

### 错误处理示例
```
Freq too low, adjusted to 100Hz
Freq too high, adjusted to 50MHz
Freq setting error: no valid digits found
Correct format: A123 (set freq to 123Hz)
```

## 技术特性

### 1. 实时控制
- 参数设置立即生效
- 无需重启或额外确认

### 2. 范围保护
- 自动检查参数范围
- 超出范围时自动调整到有效值

### 3. 状态同步
- 参数变更后自动更新串口屏显示
- 保持界面与实际状态一致

### 4. 错误处理
- 详细的错误提示信息
- 格式错误时提供正确格式说明

## 注意事项

1. **命令格式**: 必须严格按照格式发送，包括逗号结尾
2. **数值范围**: 超出范围的数值会被自动调整
3. **通道切换**: 切换通道后当前频率和幅度设置会应用到新通道
4. **实时生效**: 所有参数设置立即应用到硬件，无需额外操作

## 扩展功能

### 计数器命令
- **格式**: `1`
- **功能**: 发送递增计数器到串口屏文本框t0
- **用途**: 测试串口屏通信和显示功能

## 故障排除

### 1. 命令无响应
- 检查串口屏连接
- 确认USART2中断是否正常启动

### 2. 参数设置无效
- 检查AD9959硬件连接
- 确认DDS模块初始化是否完成

### 3. 显示不更新
- 检查串口屏通信协议
- 确认HMI显示更新任务是否在调度器中运行

## 开发者信息

- **模块**: hmi_key_handler.c/h
- **主要函数**: `Process_Parameter_Command()`
- **调度任务**: `HMI_Key_Process()` (10ms周期)
- **显示更新**: `HMI_DDS_Display_Update()` (200ms周期)
