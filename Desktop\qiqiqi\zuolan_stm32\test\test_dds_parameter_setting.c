/**
 * @file test_dds_parameter_setting.c
 * @brief DDS参数设置功能测试程序
 * @details 测试修正后的DDS参数设置功能，验证数据格式41 31 32 33 0D 0A能正确设置为123Hz
 * <AUTHOR>
 * @date 2025-07-30
 */

#include "hmi_key_handler.h"
#include "my_usart.h"
#include "stdio.h"
#include "string.h"

// 外部变量声明
extern uint8_t USART_RX_BUF[RX_BUFFER_SIZE];
extern uint16_t USART_RX_STA;

/**
 * @brief 模拟接收指定的十六进制数据
 * @param hex_data 十六进制数据数组
 * @param len 数据长度
 */
void simulate_receive_data(uint8_t *hex_data, uint16_t len)
{
    // 清空接收缓冲区
    memset(USART_RX_BUF, 0, RX_BUFFER_SIZE);
    USART_RX_STA = 0;
    
    // 复制数据到接收缓冲区
    for (int i = 0; i < len && i < RX_BUFFER_SIZE; i++) {
        USART_RX_BUF[i] = hex_data[i];
    }
    
    // 设置接收完成标志
    USART_RX_STA = 0x8000 | len;
}

/**
 * @brief 测试DDS参数设置功能
 */
void test_dds_parameter_setting(void)
{
    my_printf(&huart1, "\r\n=== DDS参数设置功能测试 ===\r\n");
    
    // 测试数据: 41 31 32 33 0D 0A (对应 A123\r\n)
    uint8_t test_data[] = {0x41, 0x31, 0x32, 0x33, 0x0D, 0x0A};
    uint16_t test_len = sizeof(test_data);
    
    my_printf(&huart1, "测试数据 (十六进制): ");
    for (int i = 0; i < test_len; i++) {
        my_printf(&huart1, "%02X ", test_data[i]);
    }
    my_printf(&huart1, "\r\n");
    
    my_printf(&huart1, "测试数据 (ASCII): ");
    for (int i = 0; i < test_len; i++) {
        if (test_data[i] >= 0x20 && test_data[i] <= 0x7E) {
            my_printf(&huart1, "%c", test_data[i]);
        } else {
            my_printf(&huart1, "[%02X]", test_data[i]);
        }
    }
    my_printf(&huart1, "\r\n");
    
    // 获取设置前的频率
    uint32_t freq_before = HMI_Get_DDS_Frequency();
    my_printf(&huart1, "设置前频率: %lu Hz\r\n", freq_before);
    
    // 模拟接收数据
    simulate_receive_data(test_data, test_len);
    
    // 处理按键数据
    my_printf(&huart1, "\r\n开始处理接收到的数据...\r\n");
    HMI_Key_Process();
    
    // 获取设置后的频率
    uint32_t freq_after = HMI_Get_DDS_Frequency();
    my_printf(&huart1, "\r\n设置后频率: %lu Hz\r\n", freq_after);
    
    // 验证结果
    if (freq_after == 123) {
        my_printf(&huart1, "✓ 测试通过: 频率正确设置为123Hz\r\n");
    } else {
        my_printf(&huart1, "✗ 测试失败: 期望123Hz，实际%luHz\r\n", freq_after);
    }
    
    my_printf(&huart1, "=== 测试完成 ===\r\n\r\n");
}

/**
 * @brief 测试其他频率值
 */
void test_other_frequencies(void)
{
    my_printf(&huart1, "\r\n=== 其他频率值测试 ===\r\n");
    
    // 测试数据集
    struct {
        uint8_t data[10];
        uint8_t len;
        uint32_t expected_freq;
        char description[30];
    } test_cases[] = {
        {{0x41, 0x31, 0x30, 0x30, 0x30, 0x0D, 0x0A}, 7, 1000, "A1000 (1kHz)"},
        {{0x41, 0x35, 0x30, 0x30, 0x0D, 0x0A}, 6, 500, "A500 (500Hz)"},
        {{0x41, 0x31, 0x30, 0x0D, 0x0A}, 5, 100, "A10->100 (最小值限制)"},
        {{0x41, 0x31, 0x32, 0x33, 0x34, 0x35, 0x0D, 0x0A}, 8, 12345, "A12345 (12.345kHz)"}
    };
    
    int num_tests = sizeof(test_cases) / sizeof(test_cases[0]);
    
    for (int t = 0; t < num_tests; t++) {
        my_printf(&huart1, "\r\n测试 %d: %s\r\n", t+1, test_cases[t].description);
        
        // 模拟接收数据
        simulate_receive_data(test_cases[t].data, test_cases[t].len);
        
        // 处理数据
        HMI_Key_Process();
        
        // 检查结果
        uint32_t actual_freq = HMI_Get_DDS_Frequency();
        if (actual_freq == test_cases[t].expected_freq) {
            my_printf(&huart1, "✓ 通过: %lu Hz\r\n", actual_freq);
        } else {
            my_printf(&huart1, "✗ 失败: 期望%lu Hz，实际%lu Hz\r\n", 
                     test_cases[t].expected_freq, actual_freq);
        }
    }
    
    my_printf(&huart1, "\r\n=== 其他频率测试完成 ===\r\n");
}

/**
 * @brief 主测试函数
 */
void run_dds_parameter_tests(void)
{
    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "DDS参数设置功能测试程序\r\n");
    my_printf(&huart1, "测试数据格式: 41 31 32 33 0D 0A -> 123Hz\r\n");
    my_printf(&huart1, "========================================\r\n");
    
    // 运行主要测试
    test_dds_parameter_setting();
    
    // 运行其他频率测试
    test_other_frequencies();
    
    my_printf(&huart1, "所有测试完成！\r\n");
}
