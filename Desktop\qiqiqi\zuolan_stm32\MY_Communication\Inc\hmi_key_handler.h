/**
 * @file hmi_key_handler.h
 * @brief 串口屏按键处理模块头文件
 * @details 该文件声明了串口屏按键处理相关的函数接口
 * <AUTHOR>
 * @date 2025-07-24
 */

#ifndef __HMI_KEY_HANDLER_H__
#define __HMI_KEY_HANDLER_H__

#include "stm32f4xx_hal.h"
#include "stdint.h"

// 按键定义
#define HMI_KEY_1    '1'  ///< 串口屏按键1
#define HMI_KEY_2    '2'  ///< 串口屏按键2
#define HMI_KEY_3    '3'  ///< 串口屏按键3
#define HMI_KEY_4    '4'  ///< 串口屏按键4（预留）
#define HMI_KEY_5    '5'  ///< 串口屏按键5（与硬件按键2功能一致）

// 参数命令定义
#define HMI_CMD_PARAM 'A'  ///< 频率设置命令标识
#define HMI_CMD_AMPLITUDE 'B'  ///< 幅度设置命令标识
#define HMI_CMD_COUNT '1'  ///< 计数命令标识

// 按键功能定义
#define HMI_KEY_ENABLE '1'     ///< 按键1: 开启DDS输出
#define HMI_KEY_DISABLE '2'    ///< 按键2: 关闭DDS输出
#define HMI_KEY_FREQ_UP '3'    ///< 按键3: 频率+100Hz
#define HMI_KEY_FREQ_DOWN '4'  ///< 按键4: 频率-100Hz
#define HMI_KEY_SPECTRUM '5'   ///< 按键5: 频谱分析
#define HMI_KEY_FREQ_1K '6'    ///< 按键6: 设置频率为1kHz
#define HMI_KEY_VOLT_UP '8'    ///< 按键8: 电压+0.1V
#define HMI_KEY_VOLT_DOWN '9'  ///< 按键9: 电压-0.1V

// 外部变量声明（在hmi_key_handler.c中定义）
extern float freq_my;    ///< 频率参数，单位MHz
extern int vpp_my;       ///< 电压峰峰值参数，单位mV

// 频率设置命令格式说明
// A123\r\n - 设置频率为123Hz (A后面直接跟数字，以回车换行结束)
// 数据示例: 41 31 32 33 0D 0A (十六进制) = A123\r\n (ASCII)
// 支持范围: 100Hz - 50MHz

// 幅度设置命令格式说明
// B2.5\r\n - 设置幅度为2.5V (B后面直接跟小数，以回车换行结束)
// 数据示例: 42 32 2E 35 0D 0A (十六进制) = B2.5\r\n (ASCII)
// 支持范围: 0V - 3V

// 按键功能详细说明
// 按键1: 开启DDS输出 - 启用DDS信号输出
// 按键2: 关闭DDS输出 - 禁用DDS信号输出
// 按键3: 频率+100Hz - 增加输出频率100Hz
// 按键4: 频率-100Hz - 减少输出频率100Hz
// 按键5: 频谱分析 - 启动快速多次扫频频谱分析功能
//   - 扫频范围: 100Hz - 100kHz (200个频率点，分辨率约500Hz)
//   - 快速扫频: 3次扫频取平均值，每点稳定时间10ms
//   - 高精度采样: 2MHz采样频率，提高FFT分析精度
//   - 通过DDS产生扫频信号，经过未知滤波器后AD采集
//   - 使用FFT分析每个频率点的响应，得到滤波器特性
//   - 自动计算斜率、通带平坦度、阻带抑制等详细参数
// 按键6: 设置频率为1kHz - 快速设置为常用测试频率1000Hz
// 按键8: 电压+0.1V - 增加DDS输出幅度0.1V (范围0-3V)
// 按键9: 电压-0.1V - 减少DDS输出幅度0.1V (范围0-3V)

// 函数声明

/**
 * @brief 串口屏按键处理主函数
 * @details 检测串口屏按键数据并执行相应功能
 * @param None
 * @retval None
 */
void HMI_Key_Process(void);

/**
 * @brief 初始化DDS控制显示
 * @details 初始化串口屏上的DDS状态显示
 * @param None
 * @retval None
 */
void HMI_DDS_Display_Init(void);

/**
 * @brief DDS状态实时更新任务
 * @details 定期更新串口屏上的DDS状态信息，用于调度器调用
 * @param None
 * @retval None
 */
void HMI_DDS_Display_Update(void);

/**
 * @brief 串口屏按键测试函数
 * @details 用于测试串口屏按键功能
 * @param None
 * @retval None
 */
void HMI_Key_Test(void);

/**
 * @brief 获取当前按键状态
 * @details 返回当前按下的按键值
 * @param None
 * @retval uint8_t 按键值('1'-'5')，无按键返回0
 */
uint8_t HMI_Get_Key_Status(void);

/**
 * @brief 清除按键状态
 * @details 清除当前按键接收状态
 * @param None
 * @retval None
 */
void HMI_Clear_Key_Status(void);

/**
 * @brief 获取DDS输出状态
 * @details 返回当前DDS输出是否开启
 * @param None
 * @retval uint8_t 1=开启，0=关闭
 */
uint8_t HMI_Get_DDS_Status(void);

/**
 * @brief 获取DDS当前频率
 * @details 返回当前设置的DDS频率值
 * @param None
 * @retval uint32_t 频率值（Hz）
 */
uint32_t HMI_Get_DDS_Frequency(void);

/**
 * @brief 设置DDS频率
 * @details 直接设置DDS频率值
 * @param frequency 频率值（Hz）
 * @retval None
 */
void HMI_Set_DDS_Frequency(uint32_t frequency);

/**
 * @brief 获取DDS波形类型
 * @details 返回当前设置的DDS波形类型
 * @param None
 * @retval uint8_t 波形类型（0=正弦波，1=方波，2=三角波）
 */
uint8_t HMI_Get_DDS_Waveform(void);

/**
 * @brief 设置DDS波形类型
 * @details 设置DDS输出的波形类型
 * @param waveform 波形类型（0=正弦波，1=方波，2=三角波）
 * @retval None
 */
void HMI_Set_DDS_Waveform(uint8_t waveform);

/**
 * @brief 获取DDS幅度
 * @details 返回当前设置的DDS幅度值
 * @param None
 * @retval uint16_t 幅度值（0-1023）
 */
uint16_t HMI_Get_DDS_Amplitude(void);

/**
 * @brief 设置DDS幅度
 * @details 设置DDS输出的幅度值
 * @param amplitude 幅度值（0-1023）
 * @retval None
 */
void HMI_Set_DDS_Amplitude(uint16_t amplitude);

#endif // __HMI_KEY_HANDLER_H__
