/**
 * @file test_spectrum_analysis.c
 * @brief 频谱分析功能测试程序
 * @details 测试按键5触发的频谱分析功能，验证扫频和滤波器响应分析
 * <AUTHOR>
 * @date 2025-07-30
 */

#include "hmi_key_handler.h"
#include "my_usart.h"
#include "AD9959.h"
#include "ad_measure.h"
#include "my_fft.h"
#include "stdio.h"

// 外部变量声明
extern uint8_t USART_RX_BUF[RX_BUFFER_SIZE];
extern uint16_t USART_RX_STA;

/**
 * @brief 模拟按键5按下
 */
void simulate_key5_press(void)
{
    // 清空接收缓冲区
    memset(USART_RX_BUF, 0, RX_BUFFER_SIZE);
    USART_RX_STA = 0;

    // 模拟按键5
    USART_RX_BUF[0] = '5';
    USART_RX_STA = 0x8001; // 设置接收完成标志和数据长度1
}

/**
 * @brief 测试频谱分析功能
 */
void test_spectrum_analysis_function(void)
{
    my_printf(&huart1, "\r\n=== 频谱分析功能测试 ===\r\n");

    // 初始化必要的模块
    my_printf(&huart1, "初始化DDS模块...\r\n");
    AD9959_Init();

    my_printf(&huart1, "初始化FFT模块...\r\n");
    fft_init();

    // 显示测试说明
    my_printf(&huart1, "\r\n测试说明:\r\n");
    my_printf(&huart1, "1. 模拟按键5按下\r\n");
    my_printf(&huart1, "2. 启动频谱分析功能\r\n");
    my_printf(&huart1, "3. DDS将产生100Hz-100kHz扫频信号\r\n");
    my_printf(&huart1, "4. 信号经过未知滤波器后被AD采集\r\n");
    my_printf(&huart1, "5. 对每个频率点进行FFT分析\r\n");
    my_printf(&huart1, "6. 分析滤波器的频率响应特性\r\n\r\n");

    // 获取初始状态
    uint32_t initial_freq = HMI_Get_DDS_Frequency();
    uint8_t initial_status = HMI_Get_DDS_Status();

    my_printf(&huart1, "初始DDS状态:\r\n");
    my_printf(&huart1, "  频率: %lu Hz\r\n", initial_freq);
    my_printf(&huart1, "  状态: %s\r\n", initial_status ? "开启" : "关闭");

    // 模拟按键5按下
    my_printf(&huart1, "\r\n模拟按键5按下...\r\n");
    simulate_key5_press();

    // 处理按键
    my_printf(&huart1, "处理按键事件...\r\n");
    HMI_Key_Process();

    // 检查最终状态
    uint32_t final_freq = HMI_Get_DDS_Frequency();
    uint8_t final_status = HMI_Get_DDS_Status();

    my_printf(&huart1, "\r\n最终DDS状态:\r\n");
    my_printf(&huart1, "  频率: %lu Hz\r\n", final_freq);
    my_printf(&huart1, "  状态: %s\r\n", final_status ? "开启" : "关闭");

    // 验证状态恢复
    if (final_freq == initial_freq && final_status == initial_status) {
        my_printf(&huart1, "✓ DDS状态已正确恢复\r\n");
    } else {
        my_printf(&huart1, "✗ DDS状态恢复异常\r\n");
    }

    my_printf(&huart1, "\r\n=== 频谱分析功能测试完成 ===\r\n");
}

/**
 * @brief 测试滤波器响应分析算法
 */
void test_filter_analysis_algorithm(void)
{
    my_printf(&huart1, "\r\n=== 滤波器分析算法测试 ===\r\n");

    // 模拟低通滤波器响应数据
    float freq_points_lp[11] = {100, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000};
    float magnitude_lp[11] = {1.0, 0.95, 0.85, 0.70, 0.50, 0.30, 0.15, 0.08, 0.04, 0.02, 0.01};

    my_printf(&huart1, "测试低通滤波器响应分析:\r\n");
    // 这里需要调用内部函数，但由于是static，我们需要通过其他方式测试
    // 或者创建一个公共的测试接口

    // 模拟带通滤波器响应数据
    float freq_points_bp[11] = {100, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000};
    float magnitude_bp[11] = {0.1, 0.3, 0.6, 0.85, 0.95, 1.0, 0.95, 0.85, 0.6, 0.3, 0.1};

    my_printf(&huart1, "测试带通滤波器响应分析:\r\n");

    // 模拟高通滤波器响应数据
    float freq_points_hp[11] = {100, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000};
    float magnitude_hp[11] = {0.01, 0.02, 0.04, 0.08, 0.15, 0.30, 0.50, 0.70, 0.85, 0.95, 1.0};

    my_printf(&huart1, "测试高通滤波器响应分析:\r\n");

    my_printf(&huart1, "=== 滤波器分析算法测试完成 ===\r\n");
}

/**
 * @brief 测试快速多次扫频参数计算
 */
void test_sweep_parameters(void)
{
    my_printf(&huart1, "\r\n=== 快速多次扫频参数测试 ===\r\n");

    const uint32_t start_freq = 100;
    const uint32_t end_freq = 100000;
    const uint32_t freq_steps = 200;      // 增加到200点
    const uint32_t freq_step_size = (end_freq - start_freq) / freq_steps;
    const uint32_t settle_time_ms = 10;   // 快速稳定时间
    const uint32_t sample_freq = 2000000; // 2MHz采样
    const uint32_t sweep_count = 3;       // 3次扫频

    my_printf(&huart1, "扫频参数:\r\n");
    my_printf(&huart1, "  起始频率: %lu Hz\r\n", start_freq);
    my_printf(&huart1, "  结束频率: %lu Hz\r\n", end_freq);
    my_printf(&huart1, "  频率步数: %lu\r\n", freq_steps);
    my_printf(&huart1, "  步进大小: %lu Hz\r\n", freq_step_size);
    my_printf(&huart1, "  稳定时间: %lu ms\r\n", settle_time_ms);
    my_printf(&huart1, "  采样频率: %lu Hz\r\n", sample_freq);
    my_printf(&huart1, "  扫频次数: %lu\r\n", sweep_count);

    // 计算总测试时间估计
    uint32_t total_time_ms = sweep_count * (freq_steps + 1) * settle_time_ms;
    my_printf(&huart1, "  估计总时间: %lu.%lu 秒\r\n", total_time_ms/1000, (total_time_ms%1000)/100);

    my_printf(&huart1, "\r\n频率点预览 (每20个点):\r\n");
    for (uint32_t step = 0; step <= freq_steps; step += 20) {
        uint32_t freq = start_freq + step * freq_step_size;
        my_printf(&huart1, "  步骤 %lu: %lu Hz\r\n", step, freq);
    }

    // 频率分辨率分析
    my_printf(&huart1, "\r\n分辨率分析:\r\n");
    my_printf(&huart1, "  频率分辨率: %lu Hz\r\n", freq_step_size);
    my_printf(&huart1, "  FFT分辨率: %.1f Hz (假设2MHz采样，1024点FFT)\r\n",
             (float)sample_freq / 1024.0f);

    my_printf(&huart1, "=== 快速多次扫频参数测试完成 ===\r\n");
}

/**
 * @brief 主测试函数
 */
void run_spectrum_analysis_tests(void)
{
    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "快速多次扫频频谱分析功能测试程序\r\n");
    my_printf(&huart1, "功能: 按键5触发快速多次扫频+FFT分析滤波器响应\r\n");
    my_printf(&huart1, "优化: 200个频率点，3次扫频取平均，10ms稳定时间\r\n");
    my_printf(&huart1, "========================================\r\n");

    // 运行扫频参数测试
    test_sweep_parameters();

    // 运行滤波器分析算法测试
    test_filter_analysis_algorithm();

    // 运行完整功能测试
    test_spectrum_analysis_function();

    my_printf(&huart1, "\r\n所有测试完成！\r\n");
    my_printf(&huart1, "注意: 实际使用时需要连接DDS输出到滤波器输入，\r\n");
    my_printf(&huart1, "      滤波器输出连接到AD输入，才能获得真实的滤波器响应。\r\n");
}
